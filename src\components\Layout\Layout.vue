<template>
  <div class="layout">
    <!-- 侧边导航 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <div
          v-for="buttonName in sidebarButtons"
          :class="`header-button ${
            buttonName.name === sidebarActiveButton ? 'is-active' : ''
          } cursor-pointer`"
          @click="rssStore.setSidebarActiveButton(buttonName.name)"
        >
          <div :class="`${buttonName.iconClass} text-xl`" />
        </div>
      </div>
      <div class="sidebar-content" v-if="showRSSNavigation">
        <RSSNavigation v-if="sidebarActiveButton === 'rss'" />
        <BookmarksNavigation v-if="sidebarActiveButton === 'bookmarks'" />
        <DirectoryNavigation v-if="sidebarActiveButton === 'directory'" />
        <ArticlesNavigation v-if="sidebarActiveButton === 'articles'" />
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content" :class="{ 'full-width': !showRSSNavigation }">
      <div class="content-header">
        <div class="flex justify-start items-center">
          <div
            :class="`header-button ${
              navigationStore.canGoBack ? 'is-active' : ''
            }`"
            @click="navigationStore.goBack"
          >
            <div class="i-material-symbols-chevron-left-rounded" />
          </div>
          <div
            :class="`header-button ${
              navigationStore.canGoForward ? 'is-active' : ''
            }`"
            @click="navigationStore.goForward"
          >
            <div class="i-material-symbols-chevron-right-rounded" />
          </div>
          <!-- 路径显示区域 -->
          <div
            class="header-button is-active flex justify-center items-center"
            v-if="showRSSNavigation"
          >
            <div class="i-material-symbols-route-outline font-size-lg" />
            <span
              class="path-segment"
              style="font-size: 12px"
              v-if="currentPath.length > 0"
              v-for="(segment, index) in currentPath"
              :key="index"
            >
              &nbsp;{{ segment }}
              <span
                class="path-separator"
                v-if="index < currentPath.length - 1"
              >
                &nbsp;/
              </span>
            </span>
            <!-- 当没有路径时显示"所有文章" -->
            <span v-else class="path-segment" style="font-size: 12px">
              &nbsp;所有文章
            </span>
          </div>
        </div>

        <!-- 按钮区域 -->
        <div class="button-area">
          <div v-for="buttonName in mainButtons" class="header-button">
            <div @click="buttonName.clickFun" :title="buttonName.name">
              <div :class="`${buttonName.iconClass} text-xl cursor-pointer`" />
            </div>
          </div>
        </div>
      </div>

      <div class="content-wrapper p-6">
        <slot />
      </div>
    </div>
  </div>

  <!-- 设置模态框 -->
  <SettingsModel :show="showSettingsModal" @close="closeSettingsModal" />

  <!-- 同步进度模态框 -->
  <SyncProgressModal
    :show="showSyncModal"
    :sync-progress="rssStore.syncProgress"
    :sync-total="rssStore.syncTotal"
    :sync-current-source="rssStore.syncCurrentSource"
    :sync-errors="rssStore.syncErrors"
    :is-syncing="rssStore.loading"
    @close="closeSyncModal"
  />

  <!-- 缓存清理模态框 -->
  <SyncProgressModal
    :show="showCacheModal"
    :cache-progress="settingsStore.cacheProgress"
    :cache-total="settingsStore.cacheTotal"
    :cache-current-source="settingsStore.cacheCurrentSource"
    :cache-errors="settingsStore.cacheErrors"
    :is-cleaning="settingsStore.isCleaningCache"
    @close="closeCacheModal"
  />
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import {
  RSSNavigation,
  BookmarksNavigation,
  DirectoryNavigation,
  ArticlesNavigation,
} from "@/components/Navigation";
import "./Layout.css";
import { useRSSStore } from "@/stores/rssStore";
import type {
  FolderNode,
  ParagraphNode,
  SubscriptionNode,
} from "@/types/rss/main-data";
import SyncProgressModal from "../Models/SyncProgressModal/SyncProgressModal.vue";
import { useNavigationStore } from "@/stores/navigationStore";
import SettingsModel from "../Models/SettingsModel/SettingsModel.vue";
import { useSettingsStore } from "@/stores/settingsStore";

const rssStore = useRSSStore();
const navigationStore = useNavigationStore();
const settingsStore = useSettingsStore();

const sidebarActiveButton = computed(() => rssStore.sidebarActiveButton);

// 同步模态框状态
const showSyncModal = ref(false);

// 缓存清理模态框状态
const showCacheModal = ref(false);

// 设置模态框状态
const showSettingsModal = ref(false);

// 关闭同步模态框
const closeSyncModal = () => {
  if (!rssStore.loading) {
    showSyncModal.value = false;
  }
};

// 关闭缓存清理模态框
const closeCacheModal = () => {
  if (!settingsStore.isCleaningCache) {
    showCacheModal.value = false;
  }
};

// 关闭设置模态框
const closeSettingsModal = () => {
  showSettingsModal.value = false;
};

// 处理显示同步弹窗事件
const handleShowSyncModal = () => {
  console.log("显示同步弹窗");
  showSyncModal.value = true;
};

// 处理隐藏同步弹窗事件
const handleHideSyncModal = () => {
  console.log("隐藏同步弹窗");
  if (!rssStore.loading) {
    showSyncModal.value = false;
  }
};

// 处理显示缓存清理弹窗事件
const handleShowCacheModal = () => {
  console.log("显示缓存清理弹窗");
  showCacheModal.value = true;
};

// 处理隐藏缓存清理弹窗事件
const handleHideCacheModal = () => {
  console.log("隐藏缓存清理弹窗");
  if (!settingsStore.isCleaningCache) {
    showCacheModal.value = false;
  }
};

// 组件挂载时添加事件监听
onMounted(() => {
  console.log("Layout 组件挂载，添加事件监听");
  window.addEventListener("show-sync-modal", handleShowSyncModal);
  window.addEventListener("hide-sync-modal", handleHideSyncModal);
  window.addEventListener("show-cache-modal", handleShowCacheModal);
  window.addEventListener("hide-cache-modal", handleHideCacheModal);
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  console.log("Layout 组件卸载，移除事件监听");
  window.removeEventListener("show-sync-modal", handleShowSyncModal);
  window.removeEventListener("hide-sync-modal", handleHideSyncModal);
  window.removeEventListener("show-cache-modal", handleShowCacheModal);
  window.removeEventListener("hide-cache-modal", handleHideCacheModal);
});

// 计算当前路径
const currentPath = computed(() => {
  if (!rssStore.currentSourceId || !rssStore.currentSourceType) {
    return []; // 显示所有文章时，不显示路径
  }

  if (rssStore.currentSourceType === "folder") {
    // 查找文件夹并构建路径
    return buildFolderPath(rssStore.currentSourceId);
  } else if (rssStore.currentSourceType === "subscription") {
    // 查找订阅源并构建路径（包含所在文件夹和订阅源名称）
    return buildSubscriptionPath(rssStore.currentSourceId);
  }

  return [];
});

// 构建文件夹路径
const buildFolderPath = (folderId: string): string[] => {
  if (!rssStore.mainData) return [];

  // 查找文件夹
  const findFolder = (
    nodes: Array<FolderNode | SubscriptionNode | ParagraphNode>,
    targetId: string,
    path: string[] = []
  ): string[] | null => {
    for (const node of nodes) {
      if (node.type === "folder" && node.id === targetId) {
        return [...path, node.name];
      } else if (node.type === "folder") {
        const found = findFolder(node.children, targetId, [...path, node.name]);
        if (found) return found;
      }
    }
    return null;
  };

  return findFolder(rssStore.mainData.children, folderId) || [];
};

// 构建订阅源路径（包含所在文件夹和订阅源名称）
const buildSubscriptionPath = (subscriptionId: string): string[] => {
  if (!rssStore.mainData) return [];

  // 查找订阅源及其所在文件夹
  const findSubscription = (
    nodes: Array<FolderNode | SubscriptionNode | ParagraphNode>,
    targetId: string,
    path: string[] = []
  ): string[] | null => {
    for (const node of nodes) {
      if (node.type === "subscription" && node.id === targetId) {
        return [...path, node.name];
      } else if (node.type === "folder") {
        const found = findSubscription(node.children, targetId, [
          ...path,
          node.name,
        ]);
        if (found) return found;
      }
    }
    return null;
  };

  return findSubscription(rssStore.mainData.children, subscriptionId) || [];
};

// 刷新当前视图
const refreshCurrentView = async () => {
  await rssStore.refreshCurrentData();
};

// 侧边栏按钮列表
const sidebarButtons = [
  {
    name: "rss",
    iconClass: "i-material-symbols-folder-outline",
  },
  {
    name: "bookmarks",
    iconClass: "i-material-symbols-bookmark-outline",
  },
  {
    name: "directory",
    iconClass: "i-material-symbols-format-align-left-rounded",
  },
  {
    name: "articles",
    iconClass: "i-material-symbols-article-outline-rounded",
  },
];

const mainButtons = [
  // {
  //   name: "refresh",
  //   iconClass: "i-material-symbols-refresh",
  //   clickFun: refreshCurrentView,
  // },
  {
    name: "sync",
    iconClass: "i-material-symbols-cloud-sync-rounded",
    clickFun: async () => {
      showSyncModal.value = true;
      try {
        await rssStore.syncAllRSSFeeds();
        showSyncModal.value = false;
      } catch (error) {
        console.error("同步失败:", error);
      }
    },
  },
  {
    name: "setting",
    iconClass: "i-material-symbols-settings-outline",
    clickFun: async () => (showSettingsModal.value = true),
  },
];

interface Props {
  showRSSNavigation?: boolean;
}

withDefaults(defineProps<Props>(), {
  showRSSNavigation: true,
});
</script>

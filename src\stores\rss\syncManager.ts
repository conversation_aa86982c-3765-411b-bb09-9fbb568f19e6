import { getMainData } from "@/services/database/mainDataService";
import { createRSSContent } from "@/services/database/rssContentService";
import { SystemTag } from "@/types/rss/rss-content";
import { Ref, ref } from "vue";
import { MessageAPI } from "@/components/Message";

export function useSyncManager(loading: Ref<Boolean>, fetchAllData: Function) {
  const syncProgress = ref(0);
  const syncTotal = ref(0);
  const syncCurrentSource = ref<string | null>(null);
  const syncErrors = ref<string[]>([]);

  // 同步所有RSS源
  const syncAllRSSFeeds = async () => {
    if (loading.value) return;

    loading.value = true;
    try {
      // 获取主数据
      const mainData = await getMainData();

      // 收集所有订阅源URL
      const subscriptionUrls: string[] = [];

      const collectSubscriptions = (nodes: Array<any>) => {
        for (const node of nodes) {
          if (node.type === "subscription") {
            subscriptionUrls.push(node.url);
          } else if (node.type === "folder") {
            collectSubscriptions(node.children);
          }
        }
      };

      collectSubscriptions(mainData.children);

      console.log(`找到 ${subscriptionUrls.length} 个订阅源需要同步`);

      // 订阅状态
      syncProgress.value = 0;
      syncTotal.value = subscriptionUrls.length;
      syncErrors.value = [];

      // 逐个同步订阅源
      for (let i = 0; i < subscriptionUrls.length; i++) {
        const url = subscriptionUrls[i];
        syncCurrentSource.value = url;
        syncProgress.value = i + 1;

        try {
          await syncSingleRSSFeed(url);
        } catch (error) {
          console.error(`同步RSS源失败: ${url}`, error);
          syncErrors.value.push(`同步失败: ${url}`);
        }
      }

      // 刷新数据
      await fetchAllData();

      // 显示同步完成通知
      MessageAPI.success("RSS同步完成");
    } finally {
      loading.value = false;
      syncCurrentSource.value = null;
    }
  };

  // 同步单个RSS源
  const syncSingleRSSFeed = async (url: string) => {
    try {
      // 使用utoolsAPI获取RSS数据
      const data = await (window as any).utoolsAPI.rss.subscribe(url);

      // 获取现有RSS内容
      const docId = `rss-browser/content/${data.feedUrl}`;
      let existingContent = null;
      try {
        existingContent = await utools.db.get(docId);
      } catch (error: any) {
        if (error.status !== 404) {
          throw error;
        }
      }

      // 确保新文章数据的完整性
      const newItemsWithIntegrity = data.items.map((item: any) => {
        const articleWithIntegrity = ensureArticleIntegrity(item, data.feedUrl);

        // 处理creator字段
        return {
          ...articleWithIntegrity,
          sourceId: data.feedUrl,
          creator: item.creator
            ? `${data.title} - ${item.creator}`
            : data.title,
        };
      });

      // 合并新旧文章，避免重复
      const existingItems = existingContent?.items || [];
      const filteredNewItems = newItemsWithIntegrity.filter((newItem: any) => {
        return !existingItems.some(
          (existingItem: any) =>
            existingItem.link === newItem.link ||
            existingItem.title === newItem.title
        );
      });

      // 如果有新文章，更新数据库
      if (filteredNewItems.length > 0 || !existingContent) {
        // 确保现有文章数据的完整性
        const existingItemsWithIntegrity = existingItems.map((item: any) =>
          ensureArticleIntegrity(item, data.feedUrl)
        );

        const allItems = [...filteredNewItems, ...existingItemsWithIntegrity];

        // 按发布时间排序，最新的在前
        allItems.sort((a, b) => {
          const dateA = new Date(a.pubDate).getTime();
          const dateB = new Date(b.pubDate).getTime();
          return dateB - dateA;
        });

        // 限制文章数量，避免数据库过大
        const limitedItems = allItems.slice(0, 100);

        await createRSSContent({
          _id: docId,
          sourceId: data.feedUrl,
          sourceTitle: data.title,
          lastUpdated: data.lastBuildDate,
          items: limitedItems,
          lastFetchTime: new Date().toISOString(),
          fetchCount: (existingContent?.fetchCount || 0) + 1,
        });
      }
    } catch (error) {
      console.error(`同步单个RSS源失败: ${url}`, error);
      throw error;
    }
  };

  /**
   * 确保RSS文章数据的完整性
   * @param article 文章对象
   * @param sourceId RSS源ID
   * @returns 处理后的文章对象
   */
  const ensureArticleIntegrity = (article: any, sourceId: string): any => {
    // 确保有ID
    const id = article.id || generateArticleId(article, sourceId);
    // 确保有添加日期
    const addedDate = article.addedDate || new Date().toISOString();
    // 确保有marks数组
    let marks = article.marks || [];

    // 处理旧格式的系统标签（带有@前缀）
    marks = marks.map((mark: string) => {
      // 如果是旧格式的系统标签，转换为新格式
      if (mark === "@已读") return SystemTag.READ;
      if (mark === "@书签") return SystemTag.BOOKMARK;
      return mark;
    });

    // 返回处理后的文章对象
    return {
      ...article,
      id,
      addedDate,
      marks,
    };
  };

  /**
   * 为RSS文章生成唯一ID
   * @param article 文章对象
   * @param sourceId RSS源ID
   * @returns string 唯一的文章ID
   */
  const generateArticleId = (article: any, sourceId: string): string => {
    // 如果文章已有ID，直接返回
    if (article.id && typeof article.id === "string") {
      return article.id;
    }

    // 生成唯一标识符的函数
    const generateHash = (str: string): string => {
      // 使用更复杂的哈希算法
      let hash = 0;

      // 如果字符串为空，返回默认值
      if (str.length === 0) return "0";

      // 使用简单的哈希算法
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash; // 转换为32位整数
      }

      // 转换为正数并转为36进制字符串
      return Math.abs(hash).toString(36);
    };

    // 获取当前时间戳，用于确保唯一性
    const timestamp = Date.now().toString(36);

    // 优先级1: 如果有guid，使用guid
    if (article.guid) {
      const guidHash = generateHash(article.guid.toString());
      return `article-${sourceId}-${guidHash}`;
    }

    // 优先级2: 如果有link，使用link
    if (article.link) {
      // 提取URL的路径部分，因为域名部分可能相同
      let urlToHash = article.link;
      try {
        // 尝试解析URL，只使用路径和查询部分
        const url = new URL(article.link);
        urlToHash = url.pathname + url.search;
      } catch (e) {
        // 如果URL解析失败，使用原始URL
        urlToHash = article.link;
      }

      const linkHash = generateHash(urlToHash);
      return `article-${sourceId}-${linkHash}-${timestamp.substring(0, 6)}`;
    }

    // 优先级3: 组合使用title、pubDate和description/content
    if (article.title || article.pubDate) {
      const title = article.title || "no-title";
      const pubDate = article.pubDate
        ? new Date(article.pubDate).toISOString()
        : "no-date";
      const content =
        article.description || article.content || article.summary || "";

      // 组合多个字段生成哈希
      const combinedData = `${title}-${pubDate}-${content.substring(0, 100)}`;
      const combinedHash = generateHash(combinedData);
      return `article-${sourceId}-${combinedHash}-${timestamp.substring(0, 6)}`;
    }

    // 优先级4: 如果以上都没有，使用随机数和时间戳确保唯一性
    const randomPart = Math.random().toString(36).substring(2, 10);
    const uniqueId = `article-${sourceId}-${randomPart}-${timestamp}`;

    return uniqueId;
  };

  return {
    // 同步状态
    syncProgress,
    syncTotal,
    syncCurrentSource,
    syncErrors,

    // 同步函数
    syncAllRSSFeeds,
    syncSingleRSSFeed,
    ensureArticleIntegrity,
  };
}

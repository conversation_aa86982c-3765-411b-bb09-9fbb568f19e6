<template>
  <div
    ref="triggerRef"
    class="tooltip-trigger"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @focus="handleFocus"
    @blur="handleBlur"
  >
    <!-- 触发元素插槽 -->
    <slot />

    <!-- Tooltip 内容 -->
    <Teleport to="body">
      <Transition
        enter-active-class="tooltip-enter-active"
        leave-active-class="tooltip-leave-active"
        enter-from-class="tooltip-enter-from"
        leave-to-class="tooltip-leave-to"
      >
        <div
          v-if="visible"
          ref="tooltipRef"
          :class="[
            'tooltip-container',
            `tooltip-${placement}`,
            `tooltip-${theme}`,
            { 'tooltip-arrow': showArrow }
          ]"
          :style="tooltipStyle"
          role="tooltip"
          :aria-describedby="ariaId"
        >
          <!-- 箭头 -->
          <div v-if="showArrow" class="tooltip-arrow-element" />
          
          <!-- 内容 -->
          <div class="tooltip-content">
            <slot name="content">
              {{ content }}
            </slot>
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'

export interface TooltipProps {
  /** 提示内容 */
  content?: string
  /** 显示位置 */
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'top-start' | 'top-end' | 'bottom-start' | 'bottom-end' | 'left-start' | 'left-end' | 'right-start' | 'right-end'
  /** 主题样式 */
  theme?: 'dark' | 'light' | 'obsidian'
  /** 触发方式 */
  trigger?: 'hover' | 'focus' | 'manual'
  /** 显示延迟 (ms) */
  showDelay?: number
  /** 隐藏延迟 (ms) */
  hideDelay?: number
  /** 是否显示箭头 */
  showArrow?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 最大宽度 */
  maxWidth?: string | number
  /** z-index */
  zIndex?: number
}

const props = withDefaults(defineProps<TooltipProps>(), {
  placement: 'top',
  theme: 'obsidian',
  trigger: 'hover',
  showDelay: 100,
  hideDelay: 100,
  showArrow: true,
  disabled: false,
  maxWidth: '200px',
  zIndex: 1000
})

const emit = defineEmits<{
  show: []
  hide: []
}>()

// 响应式引用
const triggerRef = ref<HTMLElement>()
const tooltipRef = ref<HTMLElement>()
const visible = ref(false)
const tooltipStyle = ref<Record<string, any>>({})

// 定时器
let showTimer: NodeJS.Timeout | null = null
let hideTimer: NodeJS.Timeout | null = null

// 唯一 ID
const ariaId = `tooltip-${Math.random().toString(36).substr(2, 9)}`

// 计算样式
const computedMaxWidth = computed(() => {
  if (typeof props.maxWidth === 'number') {
    return `${props.maxWidth}px`
  }
  return props.maxWidth
})

// 清除定时器
const clearTimers = () => {
  if (showTimer) {
    clearTimeout(showTimer)
    showTimer = null
  }
  if (hideTimer) {
    clearTimeout(hideTimer)
    hideTimer = null
  }
}

// 显示 tooltip
const show = () => {
  if (props.disabled || visible.value) return
  
  clearTimers()
  showTimer = setTimeout(() => {
    visible.value = true
    nextTick(() => {
      updatePosition()
      emit('show')
    })
  }, props.showDelay)
}

// 隐藏 tooltip
const hide = () => {
  if (!visible.value) return
  
  clearTimers()
  hideTimer = setTimeout(() => {
    visible.value = false
    emit('hide')
  }, props.hideDelay)
}

// 更新位置
const updatePosition = () => {
  if (!triggerRef.value || !tooltipRef.value) return

  const triggerRect = triggerRef.value.getBoundingClientRect()
  const tooltipRect = tooltipRef.value.getBoundingClientRect()
  const { innerWidth, innerHeight } = window
  
  let top = 0
  let left = 0
  const offset = 8 // 箭头偏移量

  // 根据 placement 计算位置
  switch (props.placement) {
    case 'top':
      top = triggerRect.top - tooltipRect.height - offset
      left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2
      break
    case 'top-start':
      top = triggerRect.top - tooltipRect.height - offset
      left = triggerRect.left
      break
    case 'top-end':
      top = triggerRect.top - tooltipRect.height - offset
      left = triggerRect.right - tooltipRect.width
      break
    case 'bottom':
      top = triggerRect.bottom + offset
      left = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2
      break
    case 'bottom-start':
      top = triggerRect.bottom + offset
      left = triggerRect.left
      break
    case 'bottom-end':
      top = triggerRect.bottom + offset
      left = triggerRect.right - tooltipRect.width
      break
    case 'left':
      top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2
      left = triggerRect.left - tooltipRect.width - offset
      break
    case 'left-start':
      top = triggerRect.top
      left = triggerRect.left - tooltipRect.width - offset
      break
    case 'left-end':
      top = triggerRect.bottom - tooltipRect.height
      left = triggerRect.left - tooltipRect.width - offset
      break
    case 'right':
      top = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2
      left = triggerRect.right + offset
      break
    case 'right-start':
      top = triggerRect.top
      left = triggerRect.right + offset
      break
    case 'right-end':
      top = triggerRect.bottom - tooltipRect.height
      left = triggerRect.right + offset
      break
  }

  // 边界检测和调整
  if (left < 8) left = 8
  if (left + tooltipRect.width > innerWidth - 8) {
    left = innerWidth - tooltipRect.width - 8
  }
  if (top < 8) top = 8
  if (top + tooltipRect.height > innerHeight - 8) {
    top = innerHeight - tooltipRect.height - 8
  }

  tooltipStyle.value = {
    position: 'fixed',
    top: `${top}px`,
    left: `${left}px`,
    zIndex: props.zIndex,
    maxWidth: computedMaxWidth.value
  }
}

// 事件处理
const handleMouseEnter = () => {
  if (props.trigger === 'hover') {
    show()
  }
}

const handleMouseLeave = () => {
  if (props.trigger === 'hover') {
    hide()
  }
}

const handleFocus = () => {
  if (props.trigger === 'focus') {
    show()
  }
}

const handleBlur = () => {
  if (props.trigger === 'focus') {
    hide()
  }
}

// 窗口大小变化时重新计算位置
const handleResize = () => {
  if (visible.value) {
    nextTick(() => {
      updatePosition()
    })
  }
}

// 生命周期
onMounted(() => {
  window.addEventListener('resize', handleResize)
  window.addEventListener('scroll', handleResize)
})

onUnmounted(() => {
  clearTimers()
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('scroll', handleResize)
})

// 暴露方法
defineExpose({
  show,
  hide,
  visible: computed(() => visible.value)
})
</script>

<style scoped>
/* 触发器样式 */
.tooltip-trigger {
  display: inline-block;
  position: relative;
}

/* Tooltip 容器基础样式 */
.tooltip-container {
  position: fixed;
  pointer-events: none;
  font-size: 12px;
  line-height: 1.4;
  font-weight: 500;
  border-radius: 6px;
  padding: 8px 12px;
  word-wrap: break-word;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

/* 内容样式 */
.tooltip-content {
  position: relative;
  z-index: 1;
}

/* 主题样式 */
.tooltip-obsidian {
  background: linear-gradient(135deg, #2a2c2d 0%, #2f3133 100%);
  border: 1px solid #4a5a5d;
  color: #e4e4e7;
}

.tooltip-dark {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  border: 1px solid #4b5563;
  color: #f9fafb;
}

.tooltip-light {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  color: #1f2937;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* 箭头样式 */
.tooltip-arrow-element {
  position: absolute;
  width: 8px;
  height: 8px;
  transform: rotate(45deg);
  pointer-events: none;
}

/* Obsidian 主题箭头 */
.tooltip-obsidian .tooltip-arrow-element {
  background: linear-gradient(135deg, #2a2c2d 0%, #2f3133 100%);
  border: 1px solid #4a5a5d;
}

/* 深色主题箭头 */
.tooltip-dark .tooltip-arrow-element {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  border: 1px solid #4b5563;
}

/* 浅色主题箭头 */
.tooltip-light .tooltip-arrow-element {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
}

/* 箭头位置 - 顶部 */
.tooltip-top .tooltip-arrow-element,
.tooltip-top-start .tooltip-arrow-element,
.tooltip-top-end .tooltip-arrow-element {
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  border-top: none;
  border-left: none;
}

/* 箭头位置 - 底部 */
.tooltip-bottom .tooltip-arrow-element,
.tooltip-bottom-start .tooltip-arrow-element,
.tooltip-bottom-end .tooltip-arrow-element {
  top: -5px;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  border-bottom: none;
  border-right: none;
}

/* 箭头位置 - 左侧 */
.tooltip-left .tooltip-arrow-element,
.tooltip-left-start .tooltip-arrow-element,
.tooltip-left-end .tooltip-arrow-element {
  right: -5px;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  border-left: none;
  border-bottom: none;
}

/* 箭头位置 - 右侧 */
.tooltip-right .tooltip-arrow-element,
.tooltip-right-start .tooltip-arrow-element,
.tooltip-right-end .tooltip-arrow-element {
  left: -5px;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  border-right: none;
  border-top: none;
}

/* 动画效果 */
.tooltip-enter-active,
.tooltip-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.tooltip-enter-from {
  opacity: 0;
  transform: scale(0.9) translateY(-4px);
}

.tooltip-leave-to {
  opacity: 0;
  transform: scale(0.9) translateY(-4px);
}
</style>
